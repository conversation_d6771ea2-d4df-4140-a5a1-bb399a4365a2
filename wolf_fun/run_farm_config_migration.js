const { Sequelize } = require('sequelize');
const path = require('path');

// 配置数据库连接
const sequelize = new Sequelize(
  process.env.DB_NAME || "wolf",
  process.env.DB_USER || "wolf", 
  process.env.DB_PASS || "00321zixunadmin",
  {
    host: process.env.DB_HOST || "localhost",
    dialect: "mysql",
    port: Number(process.env.DB_PORT) || 3669,
    logging: console.log,
    timezone: '+08:00',
    pool: {
      max: 10,
      min: 0,
      acquire: 60000,
      idle: 10000
    },
    dialectOptions: {
      dateStrings: true,
      typeCast: true,
      connectTimeout: 60000
    },
    define: {
      charset: "utf8mb4",
      collate: "utf8mb4_unicode_ci",
      timestamps: true,
    },
    retry: {
      max: 3,
      match: [
        /Deadlock/i,
        /Connection acquire timeout/i
      ]
    }
  }
);

async function runFarmConfigMigration() {
  try {
    // 测试数据库连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    console.log('📝 执行农场配置迁移：创建 farm_configs 表...');

    const transaction = await sequelize.transaction();

    try {
      // 检查表是否已存在
      const [results] = await sequelize.query(`
        SELECT COUNT(*) as count
        FROM information_schema.tables 
        WHERE table_schema = DATABASE() 
        AND table_name = 'farm_configs'
      `, { transaction });

      if (results[0].count > 0) {
        console.log('farm_configs 表已存在，跳过创建');
        await transaction.commit();
        return;
      }

      // 创建 farm_configs 表
      await sequelize.query(`
        CREATE TABLE farm_configs (
          id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
          grade INT NOT NULL COMMENT '等级 (0-50)',
          production INT NOT NULL COMMENT '每秒产出计算用值',
          cow INT NOT NULL COMMENT '奶牛数量',
          speed INT NOT NULL COMMENT '生产速度百分比',
          milk DECIMAL(15,3) NOT NULL COMMENT '牛奶生产',
          cost BIGINT NOT NULL COMMENT '升级花费',
          offline DECIMAL(15,3) NOT NULL COMMENT '离线产出',
          version VARCHAR(50) NOT NULL DEFAULT 'default' COMMENT '配置版本',
          is_active BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否激活',
          created_by VARCHAR(100) COMMENT '创建者',
          remark TEXT COMMENT '版本说明',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB CHARSET=utf8mb4 COMMENT='农场升级配置表'
      `, { transaction });

      console.log('✅ 创建 farm_configs 表成功');

      // 创建索引
      await sequelize.query(`
        ALTER TABLE farm_configs 
        ADD INDEX idx_grade (grade),
        ADD INDEX idx_version (version),
        ADD INDEX idx_active (is_active),
        ADD INDEX idx_created_at (created_at),
        ADD UNIQUE KEY unique_grade_version (grade, version)
      `, { transaction });

      console.log('✅ 创建索引成功');

      await transaction.commit();
      console.log('✅ 农场配置迁移完成');

    } catch (error) {
      await transaction.rollback();
      throw error;
    }

  } catch (error) {
    console.error('❌ 迁移失败:', error);
    throw error;
  } finally {
    await sequelize.close();
    console.log('🔌 数据库连接已关闭');
  }
}

// 运行迁移
runFarmConfigMigration()
  .then(() => {
    console.log('🎉 农场配置迁移执行成功！');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 迁移执行失败:', error);
    process.exit(1);
  });
