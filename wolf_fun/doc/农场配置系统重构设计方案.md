# 农场配置系统重构设计方案

## 📋 项目概述

**创建时间**: 2025年7月21日  
**项目目标**: 将硬编码的农场升级配置改为动态数据库配置系统  
**技术栈**: Node.js, TypeScript, Sequelize, MySQL, Redis  

## 🎯 需求分析

### 当前状况
- ❌ 农场升级配置硬编码在 `farmPlotConfig.ts` 中
- ❌ 配置修改需要重新部署代码
- ❌ 无法实现配置版本管理和回滚
- ❌ 策划人员无法直接调整游戏参数

### 目标要求
- ✅ 配置数据存储在数据库中，支持动态修改
- ✅ 提供Excel文件上传接口，策划可直接更新配置
- ✅ 支持配置版本管理和一键回滚
- ✅ 保持现有API兼容性，不影响游戏功能
- ✅ 添加配置缓存机制，保证性能

## 🤖 给AI的完整提示词模板

```markdown
# 农场配置系统重构任务

## 项目背景
我有一个Node.js/TypeScript游戏后端项目，目前农场升级配置是硬编码在代码中的，现在需要改为动态配置系统。

## 当前文件结构
```
wolf_fun/src/
├── config/farmPlotConfig.ts          # 硬编码配置数组
├── models/FarmPlot.ts                # 农场区块模型
├── controllers/farmPlotController.ts # 农场控制器
├── services/farmPlotService.ts       # 农场服务
└── utils/bigNumberConfig.ts          # 计算工具类
```

## 配置数据结构
详见附件 `农场升级配置表.md`，包含0-50级共51条配置记录：
- grade: 等级
- production: 每秒产出计算用值
- cow: 奶牛数量  
- speed: 生产速度百分比
- milk: 牛奶生产量
- cost: 升级花费
- offline: 离线产出

## 目标要求

### 1. 创建配置数据表
```sql
CREATE TABLE farm_configs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  grade INT NOT NULL,                    -- 等级 (0-50)
  production INT NOT NULL,               -- 每秒产出计算用值
  cow INT NOT NULL,                     -- 奶牛数量
  speed INT NOT NULL,                   -- 生产速度百分比
  milk DECIMAL(15,3) NOT NULL,          -- 牛奶生产
  cost BIGINT NOT NULL,                 -- 升级花费
  offline DECIMAL(15,3) NOT NULL,       -- 离线产出
  version VARCHAR(50) NOT NULL DEFAULT 'default',  -- 配置版本
  is_active BOOLEAN DEFAULT FALSE,       -- 是否激活
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_grade (grade),
  INDEX idx_version (version),
  INDEX idx_active (is_active),
  UNIQUE KEY unique_grade_version (grade, version)
);
```

### 2. 管理员接口设计
**上传配置接口:**
```
POST /api/admin/farm-config/upload
Content-Type: multipart/form-data
Body: excelFile (fram.xlsx格式)
```

**配置管理接口:**
```
GET    /api/admin/farm-config/versions     # 获取所有配置版本
GET    /api/admin/farm-config/current      # 获取当前激活配置
POST   /api/admin/farm-config/activate     # 激活指定版本
DELETE /api/admin/farm-config/version/{version} # 删除版本
```

### 3. 代码重构要求

#### A. 创建数据库模型
文件: `src/models/FarmConfig.ts`
```typescript
interface FarmConfigAttributes {
  id: number;
  grade: number;
  production: number;
  cow: number;
  speed: number;
  milk: number;
  cost: number;
  offline: number;
  version: string;
  isActive: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}
```

#### B. 重构配置服务
文件: `src/config/farmPlotConfig.ts` 
原有函数保持不变，但内部改为数据库查询：
- `getFarmPlotBarnCount(level)` 
- `getFarmPlotProductionSpeed(level)`
- `getFarmPlotUnlockCost(plotNumber)`
- `getFarmPlotMilkProduction(plotNumber, level)`
- `getFarmPlotUpgradeCost(plotNumber, level)`

#### C. 创建配置服务
文件: `src/services/farmConfigService.ts`
```typescript
class FarmConfigService {
  async getCurrentConfig(): Promise<FarmConfig[]>
  async getConfigByGrade(grade: number): Promise<FarmConfig>
  async uploadNewConfig(excelData: any[]): Promise<string>
  async activateVersion(version: string): Promise<boolean>
  async getAllVersions(): Promise<string[]>
  async rollbackToPrevious(): Promise<boolean>
}
```

#### D. 创建管理控制器
文件: `src/controllers/farmConfigController.ts`
处理Excel上传、配置管理等管理员操作

#### E. 添加缓存机制
使用Redis缓存当前配置：
- 缓存键: `farm_config:active`
- 过期时间: 1小时
- 更新策略: 配置变更时清除缓存

### 4. Excel文件处理
- 支持 `.xlsx` 格式，结构与 `fram.xlsx` 相同
- 7个字段: grade, production, cow, speed, milk, cost, offline
- 51行数据 (等级0-50)
- 数据验证: 检查完整性、数值范围、递增性等
- 错误处理: 详细的错误信息返回

### 5. 版本管理功能
- 每次上传生成新版本号 (时间戳格式)
- 支持版本间切换，不删除历史数据
- 提供版本对比功能
- 一键回滚到上一版本

## 实现要求

### 技术要求
1. 使用Sequelize ORM操作数据库
2. 保持现有代码结构和风格
3. 添加完整的错误处理机制
4. 使用事务确保数据一致性
5. 添加适当的数据验证
6. 支持并发安全

### 兼容性要求
1. 现有游戏API接口保持不变
2. 现有数据库表结构不受影响
3. 支持平滑迁移，零停机部署
4. 向后兼容，支持配置降级

### 性能要求
1. 配置查询响应时间 < 10ms
2. 支持Redis缓存，减少数据库查询
3. Excel上传处理时间 < 5秒
4. 支持大并发配置读取

## 请完成以下任务

### 阶段一: 数据库和模型
1. 创建 `FarmConfig` 模型文件
2. 创建数据库迁移文件
3. 编写数据初始化脚本 (从现有配置导入)

### 阶段二: 配置服务重构  
1. 重构 `farmPlotConfig.ts` 改为动态查询
2. 创建 `farmConfigService.ts` 配置管理服务
3. 添加Redis缓存层
4. 保持现有函数接口兼容性

### 阶段三: 管理接口开发
1. 创建 `farmConfigController.ts` 管理控制器
2. 实现Excel文件上传解析功能
3. 添加配置版本管理接口
4. 实现配置激活和回滚功能

### 阶段四: 路由和中间件
1. 添加管理员权限验证中间件
2. 创建配置管理路由
3. 更新主应用路由配置
4. 添加API文档

### 阶段五: 测试和验证
1. 编写单元测试覆盖核心功能
2. 创建集成测试验证API
3. 提供测试脚本和示例数据
4. 性能测试和压力测试

## 预期输出

### 新增文件
- `src/models/FarmConfig.ts`
- `src/services/farmConfigService.ts`  
- `src/controllers/farmConfigController.ts`
- `src/routes/farmConfigRoutes.ts`
- `migrations/create-farm-configs.js`
- `test/farmConfig.test.js`

### 修改文件
- `src/config/farmPlotConfig.ts` (重构为动态查询)
- `src/app.ts` (添加新路由)
- `src/models/index.ts` (注册新模型)

### 配置文件
- 环境变量配置
- Redis缓存配置
- 管理员权限配置

请严格按照现有代码风格实现，确保代码质量和系统稳定性。
```

## 🏗️ 技术架构设计

### 系统架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   管理后台      │    │   游戏客户端    │    │   Excel文件     │
│  (上传配置)     │    │  (使用配置)     │    │  (配置数据)     │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          ▼                      ▼                      ▼
┌─────────────────────────────────────────────────────────────────┐
│                        API网关层                                │
├─────────────────────────────────────────────────────────────────┤
│              管理接口              │           游戏接口          │
│  /api/admin/farm-config/*         │  /api/farm-plot/*          │
└─────────┬───────────────────────────┬─────────────────────────────┘
          ▼                           ▼
┌─────────────────┐              ┌─────────────────┐
│  配置管理服务    │              │   农场服务      │
│ FarmConfigSvc   │◄────────────►│ FarmPlotSvc     │
└─────────┬───────┘              └─────────┬───────┘
          │                               │
          ▼                               ▼
┌─────────────────────────────────────────────────────────────────┐
│                         缓存层 (Redis)                         │
│  farm_config:active  │  farm_config:versions                   │
└─────────┬───────────────────────────────────────────────────────┘
          ▼
┌─────────────────────────────────────────────────────────────────┐
│                        数据库层 (MySQL)                        │
│  farm_configs 表  │  farm_plots 表  │  其他游戏表               │
└─────────────────────────────────────────────────────────────────┘
```

### 数据流设计
```
配置上传流程:
Excel文件 → 解析验证 → 生成版本 → 存储数据库 → 清除缓存 → 返回结果

配置读取流程:
游戏请求 → 检查缓存 → 缓存命中?[是→返回数据 / 否→查询数据库] → 更新缓存 → 返回数据

配置切换流程:
管理请求 → 验证权限 → 更新激活状态 → 清除缓存 → 记录日志 → 返回结果
```

## 📊 数据库设计

### farm_configs 表结构
```sql
CREATE TABLE farm_configs (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  grade INT NOT NULL COMMENT '等级 (0-50)',
  production INT NOT NULL COMMENT '每秒产出计算用值',
  cow INT NOT NULL COMMENT '奶牛数量',
  speed INT NOT NULL COMMENT '生产速度百分比',
  milk DECIMAL(15,3) NOT NULL COMMENT '牛奶生产',
  cost BIGINT NOT NULL COMMENT '升级花费',
  offline DECIMAL(15,3) NOT NULL COMMENT '离线产出',
  version VARCHAR(50) NOT NULL DEFAULT 'default' COMMENT '配置版本',
  is_active BOOLEAN DEFAULT FALSE COMMENT '是否激活',
  created_by VARCHAR(100) COMMENT '创建者',
  remark TEXT COMMENT '版本说明',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_grade (grade),
  INDEX idx_version (version),
  INDEX idx_active (is_active),
  INDEX idx_created_at (created_at),
  UNIQUE KEY unique_grade_version (grade, version)
) ENGINE=InnoDB CHARSET=utf8mb4 COMMENT='农场升级配置表';
```

### 配置版本管理表
```sql
CREATE TABLE farm_config_versions (
  id INT PRIMARY KEY AUTO_INCREMENT,
  version VARCHAR(50) NOT NULL UNIQUE COMMENT '版本号',
  name VARCHAR(100) NOT NULL COMMENT '版本名称',
  description TEXT COMMENT '版本描述',
  is_active BOOLEAN DEFAULT FALSE COMMENT '是否激活',
  config_count INT DEFAULT 0 COMMENT '配置条数',
  created_by VARCHAR(100) COMMENT '创建者',
  activated_at TIMESTAMP NULL COMMENT '激活时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  INDEX idx_active (is_active),
  INDEX idx_created_at (created_at)
) ENGINE=InnoDB CHARSET=utf8mb4 COMMENT='配置版本管理表';
```

## 🔌 API接口设计

### 管理员接口

#### 1. 上传配置文件
```http
POST /api/admin/farm-config/upload
Content-Type: multipart/form-data
Authorization: Bearer {admin_token}

Body:
- excelFile: File (fram.xlsx格式)
- version_name: String (可选，版本名称)
- description: String (可选，版本描述)

Response:
{
  "ok": true,
  "data": {
    "version": "20250721-163045",
    "configCount": 51,
    "uploadedBy": "<EMAIL>",
    "createdAt": "2025-07-21T16:30:45.123Z"
  },
  "message": "配置上传成功"
}
```

#### 2. 获取配置版本列表
```http
GET /api/admin/farm-config/versions
Authorization: Bearer {admin_token}

Response:
{
  "ok": true,
  "data": {
    "versions": [
      {
        "version": "20250721-163045",
        "name": "2025年7月版本",
        "description": "调整后期升级成本",
        "isActive": true,
        "configCount": 51,
        "createdBy": "<EMAIL>",
        "createdAt": "2025-07-21T16:30:45.123Z"
      }
    ],
    "totalVersions": 5,
    "activeVersion": "20250721-163045"
  }
}
```

#### 3. 激活指定版本
```http
POST /api/admin/farm-config/activate
Content-Type: application/json
Authorization: Bearer {admin_token}

Body:
{
  "version": "20250721-163045",
  "remark": "激活原因说明"
}

Response:
{
  "ok": true,
  "data": {
    "previousVersion": "20250720-120000",
    "newVersion": "20250721-163045",
    "activatedAt": "2025-07-21T16:35:12.456Z"
  },
  "message": "配置版本已激活"
}
```

#### 4. 获取当前配置详情
```http
GET /api/admin/farm-config/current
Authorization: Bearer {admin_token}

Response:
{
  "ok": true,
  "data": {
    "version": "20250721-163045",
    "configs": [
      {
        "grade": 0,
        "production": 0,
        "cow": 0,
        "speed": 0,
        "milk": 0,
        "cost": 13096,
        "offline": 0
      }
      // ... 更多配置
    ],
    "totalConfigs": 51
  }
}
```

### 游戏接口 (保持兼容)

现有的农场相关接口保持不变，但内部实现改为读取数据库配置：
- `GET /api/farm-plot/get` - 获取农场信息
- `POST /api/farm-plot/upgrade` - 升级农场
- `POST /api/farm-plot/unlock` - 解锁农场区块
- 等等...

## ⚡ 缓存策略设计

### Redis缓存结构
```
缓存键设计:
- farm_config:active              # 当前激活的完整配置
- farm_config:active:grade:{n}    # 指定等级的配置
- farm_config:versions           # 版本列表
- farm_config:version:{version}  # 指定版本的配置

缓存策略:
- TTL: 1小时 (3600秒)
- 更新: 配置变更时立即清除相关缓存
- 预热: 系统启动时预加载当前配置
- 降级: 缓存失效时直接查询数据库
```

### 缓存更新流程
```typescript
// 配置更新时的缓存清理
async function clearConfigCache(version?: string) {
  const keys = [
    'farm_config:active',
    'farm_config:active:grade:*',
    'farm_config:versions'
  ];
  
  if (version) {
    keys.push(`farm_config:version:${version}`);
  }
  
  await redis.del(keys);
}
```

## 📋 实施步骤指南

### Phase 1: 数据库基础
- [ ] 创建 `FarmConfig` 模型
- [ ] 编写数据库迁移文件
- [ ] 初始化默认配置数据
- [ ] 测试数据库操作

### Phase 2: 配置服务重构
- [ ] 重构 `farmPlotConfig.ts`
- [ ] 创建 `farmConfigService.ts`
- [ ] 添加Redis缓存支持
- [ ] 保证API兼容性

### Phase 3: 管理接口开发
- [ ] 创建管理控制器
- [ ] 实现Excel上传解析
- [ ] 版本管理功能
- [ ] 权限验证中间件

### Phase 4: 集成测试
- [ ] 单元测试编写
- [ ] API集成测试
- [ ] 性能压力测试
- [ ] 数据迁移测试

### Phase 5: 部署上线
- [ ] 生产环境部署
- [ ] 数据迁移执行
- [ ] 功能验证测试
- [ ] 监控告警配置

## 🔧 代码重构清单

### 需要修改的现有文件
```
src/config/farmPlotConfig.ts
├── getFarmPlotBarnCount()      → 查询 farm_configs.cow
├── getFarmPlotProductionSpeed() → 查询 farm_configs.speed  
├── getFarmPlotUnlockCost()     → 现有逻辑保持不变
├── getFarmPlotMilkProduction() → 查询 farm_configs.milk
└── getFarmPlotUpgradeCost()    → 查询 farm_configs.cost

src/models/FarmPlot.ts
├── calculateBaseProduction()   → 使用新配置服务
├── calculateUnlockCost()       → 保持现有逻辑
└── upgrade()                  → 使用新配置数据

src/services/farmPlotService.ts
└── 所有使用配置的地方都需要改为动态查询

src/utils/bigNumberConfig.ts
└── FarmPlotCalculator相关方法需要接收配置参数
```

### 需要新建的文件
```
src/models/FarmConfig.ts                    # 配置模型
src/models/FarmConfigVersion.ts             # 版本管理模型
src/services/farmConfigService.ts           # 配置管理服务
src/controllers/farmConfigController.ts     # 管理控制器
src/routes/farmConfigRoutes.ts             # 配置路由
src/middlewares/adminAuth.ts               # 管理员权限中间件
migrations/YYYYMMDD-create-farm-configs.js # 数据库迁移
test/farmConfig.test.js                    # 单元测试
scripts/migrate-farm-config.js             # 数据迁移脚本
```

## 🚨 风险控制和注意事项

### 数据安全
- ✅ 配置变更前自动备份当前版本
- ✅ 支持一键回滚机制
- ✅ 所有变更操作记录日志
- ✅ 管理员权限验证和操作审计

### 性能保障
- ✅ Redis缓存减少数据库压力
- ✅ 数据库索引优化查询性能
- ✅ 配置读取异步化处理
- ✅ 支持配置预加载和热更新

### 兼容性保证
- ✅ 现有API接口保持不变
- ✅ 渐进式迁移，支持灰度发布
- ✅ 配置降级策略，确保系统可用性
- ✅ 向后兼容，支持老版本客户端

### 错误处理
- ✅ Excel文件格式验证
- ✅ 配置数据完整性检查
- ✅ 数据库事务保证一致性
- ✅ 详细的错误日志和监控

## 📈 监控和运维

### 关键指标监控
- 配置查询响应时间
- 缓存命中率
- Excel上传成功率
- 配置变更频率
- 数据库连接池状态

### 告警规则
- 配置查询响应时间 > 100ms
- 缓存命中率 < 90%
- Excel上传失败率 > 5%
- 数据库连接数过高

### 日志记录
- 所有配置变更操作
- Excel文件上传记录
- 版本激活/回滚记录
- 系统错误和异常

---

**文档版本**: v1.0  
**最后更新**: 2025-07-21  
**文档作者**: 系统架构师  
**审核状态**: 待审核
