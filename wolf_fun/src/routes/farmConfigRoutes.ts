import express from 'express';
import multer from 'multer';
import farmConfigController from '../controllers/farmConfigController';
import { walletAuthMiddleware } from '../middlewares/walletAuth';

const router = express.Router();

// 配置multer用于文件上传
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
    files: 1
  },
  fileFilter: (req, file, cb) => {
    const allowedMimeTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel', // .xls
    ];
    
    if (allowedMimeTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('请上传有效的Excel文件 (.xlsx 或 .xls)'));
    }
  }
});

/**
 * @swagger
 * components:
 *   schemas:
 *     FarmConfig:
 *       type: object
 *       properties:
 *         grade:
 *           type: integer
 *           description: 等级
 *           minimum: 0
 *           maximum: 50
 *         production:
 *           type: integer
 *           description: 每秒产出计算用值
 *         cow:
 *           type: integer
 *           description: 奶牛数量
 *         speed:
 *           type: integer
 *           description: 生产速度百分比
 *         milk:
 *           type: number
 *           description: 牛奶生产值
 *         cost:
 *           type: integer
 *           description: 升级花费
 *         offline:
 *           type: number
 *           description: 离线产出
 *         version:
 *           type: string
 *           description: 配置版本
 *     
 *     FarmConfigVersion:
 *       type: object
 *       properties:
 *         version:
 *           type: string
 *           description: 版本号
 *         name:
 *           type: string
 *           description: 版本名称
 *         isActive:
 *           type: boolean
 *           description: 是否为激活版本
 *         configCount:
 *           type: integer
 *           description: 配置条目数量
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *         createdBy:
 *           type: string
 *           description: 创建者
 *         remark:
 *           type: string
 *           description: 备注说明
 */

/**
 * @swagger
 * /api/farm-config/current:
 *   get:
 *     summary: 获取当前激活的配置
 *     tags: [Farm Config]
 *     responses:
 *       200:
 *         description: 成功获取当前配置
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     configs:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/FarmConfig'
 *                     totalConfigs:
 *                       type: integer
 *                     message:
 *                       type: string
 *       500:
 *         description: 服务器错误
 */
router.get('/current', farmConfigController.getCurrentConfig);

/**
 * @swagger
 * /api/farm-config/grade/{grade}:
 *   get:
 *     summary: 根据等级获取配置
 *     tags: [Farm Config]
 *     parameters:
 *       - in: path
 *         name: grade
 *         required: true
 *         schema:
 *           type: integer
 *           minimum: 0
 *           maximum: 50
 *         description: 农场等级
 *     responses:
 *       200:
 *         description: 成功获取配置
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     config:
 *                       $ref: '#/components/schemas/FarmConfig'
 *                     message:
 *                       type: string
 *       400:
 *         description: 参数错误
 *       404:
 *         description: 未找到配置
 *       500:
 *         description: 服务器错误
 */
router.get('/grade/:grade', farmConfigController.getConfigByGrade);

/**
 * @swagger
 * /api/farm-config/template:
 *   get:
 *     summary: 获取配置模板文件
 *     tags: [Farm Config]
 *     responses:
 *       200:
 *         description: Excel模板文件
 *         content:
 *           application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
 *             schema:
 *               type: string
 *               format: binary
 *       500:
 *         description: 服务器错误
 */
router.get('/template', farmConfigController.getConfigTemplate);

/**
 * @swagger
 * /api/farm-config/versions:
 *   get:
 *     summary: 获取所有配置版本
 *     tags: [Farm Config]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 成功获取版本列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     versions:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/FarmConfigVersion'
 *                     totalVersions:
 *                       type: integer
 *                     message:
 *                       type: string
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
router.get('/versions', walletAuthMiddleware, farmConfigController.getAllVersions);

/**
 * @swagger
 * /api/farm-config/cache/stats:
 *   get:
 *     summary: 获取缓存统计信息
 *     tags: [Farm Config]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 成功获取缓存统计
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     cacheSize:
 *                       type: integer
 *                       description: 缓存条目数量
 *                     cacheKeys:
 *                       type: array
 *                       items:
 *                         type: string
 *                       description: 缓存键列表
 *                     message:
 *                       type: string
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
router.get('/cache/stats', walletAuthMiddleware, farmConfigController.getCacheStats);

/**
 * @swagger
 * /api/farm-config/upload:
 *   post:
 *     summary: 上传新配置文件
 *     tags: [Farm Config]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: Excel配置文件
 *               versionName:
 *                 type: string
 *                 description: 版本名称（可选）
 *               description:
 *                 type: string
 *                 description: 版本描述（可选）
 *             required:
 *               - file
 *     responses:
 *       200:
 *         description: 配置上传成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     version:
 *                       type: string
 *                       description: 生成的版本号
 *                     fileName:
 *                       type: string
 *                       description: 文件名
 *                     fileSize:
 *                       type: integer
 *                       description: 文件大小
 *                     message:
 *                       type: string
 *       400:
 *         description: 请求错误（文件类型不正确等）
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
router.post('/upload', walletAuthMiddleware, upload.single('file'), farmConfigController.uploadConfig);

/**
 * @swagger
 * /api/farm-config/preview:
 *   post:
 *     summary: 预览配置文件内容
 *     tags: [Farm Config]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: Excel配置文件
 *             required:
 *               - file
 *     responses:
 *       200:
 *         description: 预览成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     fileName:
 *                       type: string
 *                     fileSize:
 *                       type: integer
 *                     sheetName:
 *                       type: string
 *                     totalRows:
 *                       type: integer
 *                     previewData:
 *                       type: array
 *                       description: 预览数据（前10行）
 *                     message:
 *                       type: string
 *       400:
 *         description: 请求错误
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
router.post('/preview', walletAuthMiddleware, upload.single('file'), farmConfigController.previewConfig);

/**
 * @swagger
 * /api/farm-config/activate/{version}:
 *   post:
 *     summary: 激活指定版本
 *     tags: [Farm Config]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: version
 *         required: true
 *         schema:
 *           type: string
 *         description: 版本号
 *     responses:
 *       200:
 *         description: 版本激活成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     version:
 *                       type: string
 *                     message:
 *                       type: string
 *       400:
 *         description: 参数错误或版本不存在
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
router.post('/activate/:version', walletAuthMiddleware, farmConfigController.activateVersion);

/**
 * @swagger
 * /api/farm-config/rollback:
 *   post:
 *     summary: 回滚到上一版本
 *     tags: [Farm Config]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 回滚成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     previousVersion:
 *                       type: string
 *                       description: 回滚到的版本
 *                     message:
 *                       type: string
 *       400:
 *         description: 没有可回滚的版本
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
router.post('/rollback', walletAuthMiddleware, farmConfigController.rollbackToPrevious);

/**
 * @swagger
 * /api/farm-config/initialize:
 *   post:
 *     summary: 初始化默认配置
 *     tags: [Farm Config]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 初始化成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     message:
 *                       type: string
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
router.post('/initialize', walletAuthMiddleware, farmConfigController.initializeDefaultConfig);

/**
 * @swagger
 * /api/farm-config/delete/{version}:
 *   delete:
 *     summary: 删除指定版本
 *     tags: [Farm Config]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: version
 *         required: true
 *         schema:
 *           type: string
 *         description: 版本号
 *     responses:
 *       200:
 *         description: 版本删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     version:
 *                       type: string
 *                     message:
 *                       type: string
 *       400:
 *         description: 参数错误或版本不能删除
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
router.delete('/delete/:version', walletAuthMiddleware, farmConfigController.deleteVersion);

export default router;
