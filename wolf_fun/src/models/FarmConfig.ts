import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../config/db';

// 数据库字段接口
interface FarmConfigAttributes {
  id: number;
  grade: number;
  production: number;
  cow: number;
  speed: number;
  milk: number;
  cost: number;
  offline: number;
  version: string;
  isActive: boolean;
  createdBy?: string;
  remark?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

// 创建时的可选字段
interface FarmConfigCreationAttributes extends Optional<FarmConfigAttributes, 'id' | 'createdAt' | 'updatedAt' | 'createdBy' | 'remark'> {}

// 模型类
class FarmConfig extends Model<FarmConfigAttributes, FarmConfigCreationAttributes> implements FarmConfigAttributes {
  public id!: number;
  public grade!: number;
  public production!: number;
  public cow!: number;
  public speed!: number;
  public milk!: number;
  public cost!: number;
  public offline!: number;
  public version!: string;
  public isActive!: boolean;
  public createdBy?: string;
  public remark?: string;

  // 时间戳
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  /**
   * 静态方法：获取当前激活的配置
   */
  static async getActiveConfigs(): Promise<FarmConfig[]> {
    return await FarmConfig.findAll({
      where: { isActive: true },
      order: [['grade', 'ASC']]
    });
  }

  /**
   * 静态方法：根据等级获取配置
   */
  static async getConfigByGrade(grade: number): Promise<FarmConfig | null> {
    return await FarmConfig.findOne({
      where: { 
        grade: grade,
        isActive: true 
      }
    });
  }

  /**
   * 静态方法：获取所有版本
   */
  static async getAllVersions(): Promise<string[]> {
    const versions = await FarmConfig.findAll({
      attributes: ['version'],
      group: ['version'],
      order: [['created_at', 'DESC']]
    });
    return versions.map(v => v.version);
  }

  /**
   * 静态方法：激活指定版本
   */
  static async activateVersion(version: string): Promise<boolean> {
    try {
      // 开启事务
      const transaction = await sequelize.transaction();
      
      try {
        // 1. 将所有配置设为非激活状态
        await FarmConfig.update(
          { isActive: false },
          { 
            where: {},
            transaction 
          }
        );

        // 2. 激活指定版本的配置
        const [affectedCount] = await FarmConfig.update(
          { isActive: true },
          { 
            where: { version },
            transaction 
          }
        );

        await transaction.commit();
        return affectedCount > 0;
      } catch (error) {
        await transaction.rollback();
        throw error;
      }
    } catch (error) {
      console.error('激活版本失败:', error);
      return false;
    }
  }

  /**
   * 静态方法：删除指定版本
   */
  static async deleteVersion(version: string): Promise<boolean> {
    try {
      // 检查是否为激活版本
      const activeConfig = await FarmConfig.findOne({
        where: { version, isActive: true }
      });
      
      if (activeConfig) {
        throw new Error('无法删除当前激活的版本');
      }

      const deletedCount = await FarmConfig.destroy({
        where: { version }
      });

      return deletedCount > 0;
    } catch (error) {
      console.error('删除版本失败:', error);
      return false;
    }
  }
}

// 定义模型结构
FarmConfig.init({
  id: {
    type: DataTypes.INTEGER,
    autoIncrement: true,
    primaryKey: true,
    comment: '主键'
  },
  grade: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '等级 (0-50)',
    validate: {
      min: 0,
      max: 50
    }
  },
  production: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '每秒产出计算用值'
  },
  cow: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '奶牛数量',
    validate: {
      min: 0
    }
  },
  speed: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '生产速度百分比',
    validate: {
      min: 0
    }
  },
  milk: {
    type: DataTypes.DECIMAL(15, 3),
    allowNull: false,
    comment: '牛奶生产',
    validate: {
      min: 0
    }
  },
  cost: {
    type: DataTypes.BIGINT,
    allowNull: false,
    comment: '升级花费',
    validate: {
      min: 0
    }
  },
  offline: {
    type: DataTypes.DECIMAL(15, 3),
    allowNull: false,
    comment: '离线产出',
    validate: {
      min: 0
    }
  },
  version: {
    type: DataTypes.STRING(50),
    allowNull: false,
    defaultValue: 'default',
    comment: '配置版本'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    field: 'is_active',
    comment: '是否激活'
  },
  createdBy: {
    type: DataTypes.STRING(100),
    allowNull: true,
    field: 'created_by',
    comment: '创建者'
  },
  remark: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '版本说明'
  }
}, {
  sequelize,
  modelName: 'FarmConfig',
  tableName: 'farm_configs',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['grade']
    },
    {
      fields: ['version']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['created_at']
    },
    {
      unique: true,
      fields: ['grade', 'version'],
      name: 'unique_grade_version'
    }
  ],
  comment: '农场升级配置表'
});

export default FarmConfig;
