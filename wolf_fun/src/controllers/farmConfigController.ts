import { Request, Response } from 'express';
import { MyRequest } from '../types/customRequest';
import { successResponse, errorResponse } from '../utils/responseUtil';
import { tFromRequest } from '../i18n';
import farmConfigService from '../services/farmConfigService';
import FarmConfig from '../models/FarmConfig';

class FarmConfigController {
  /**
   * 获取当前激活的配置
   */
  public async getCurrentConfig(req: MyRequest, res: Response): Promise<void> {
    try {
      const configs = await farmConfigService.getCurrentConfig();
      
      res.json(successResponse({
        configs: configs.map(config => ({
          grade: config.grade,
          production: config.production,
          cow: config.cow,
          speed: config.speed,
          milk: parseFloat(config.milk.toString()),
          cost: parseInt(config.cost.toString()),
          offline: parseFloat(config.offline.toString()),
          version: config.version
        })),
        totalConfigs: configs.length,
        message: '获取当前配置成功'
      }));
    } catch (error: any) {
      console.error('获取当前配置失败:', error);
      res.status(500).json(errorResponse(error.message || '获取配置失败'));
    }
  }

  /**
   * 根据等级获取配置
   */
  public async getConfigByGrade(req: MyRequest, res: Response): Promise<void> {
    try {
      const { grade } = req.params;
      const gradeNumber = parseInt(grade);
      
      if (isNaN(gradeNumber) || gradeNumber < 0 || gradeNumber > 50) {
        res.status(400).json(errorResponse('等级必须在0-50之间'));
        return;
      }
      
      const config = await farmConfigService.getConfigByGrade(gradeNumber);
      
      if (!config) {
        res.status(404).json(errorResponse('未找到指定等级的配置'));
        return;
      }
      
      res.json(successResponse({
        config: {
          grade: config.grade,
          production: config.production,
          cow: config.cow,
          speed: config.speed,
          milk: parseFloat(config.milk.toString()),
          cost: parseInt(config.cost.toString()),
          offline: parseFloat(config.offline.toString()),
          version: config.version
        },
        message: '获取配置成功'
      }));
    } catch (error: any) {
      console.error('获取配置失败:', error);
      res.status(500).json(errorResponse(error.message || '获取配置失败'));
    }
  }

  /**
   * 上传新配置文件
   */
  public async uploadConfig(req: MyRequest, res: Response): Promise<void> {
    try {
      // 检查是否有文件上传
      if (!req.file) {
        res.status(400).json(errorResponse(tFromRequest(req, 'errors.noFileUploaded') || '请上传Excel文件'));
        return;
      }

      // 检查文件类型
      const allowedMimeTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
        'application/vnd.ms-excel', // .xls
      ];

      if (!allowedMimeTypes.includes(req.file.mimetype)) {
        res.status(400).json(errorResponse('请上传有效的Excel文件 (.xlsx 或 .xls)'));
        return;
      }

      const { versionName, description } = req.body;
      const createdBy = req.user?.userId?.toString() || req.user?.walletAddress || 'unknown';
      
      // 上传并处理配置
      const version = await farmConfigService.uploadNewConfig(
        req.file.buffer,
        versionName,
        description,
        createdBy
      );
      
      res.json(successResponse({
        version,
        fileName: req.file.originalname,
        fileSize: req.file.size,
        message: '配置文件上传成功'
      }));
    } catch (error: any) {
      console.error('上传配置文件失败:', error);
      res.status(500).json(errorResponse(error.message || '上传失败'));
    }
  }

  /**
   * 获取所有配置版本
   */
  public async getAllVersions(req: MyRequest, res: Response): Promise<void> {
    try {
      const versions = await farmConfigService.getAllVersions();
      
      res.json(successResponse({
        versions: versions.map(v => ({
          version: v.version,
          name: v.name || v.version,
          isActive: v.isActive,
          configCount: v.configCount,
          createdAt: v.createdAt,
          createdBy: v.createdBy,
          remark: v.remark
        })),
        totalVersions: versions.length,
        message: '获取版本列表成功'
      }));
    } catch (error: any) {
      console.error('获取版本列表失败:', error);
      res.status(500).json(errorResponse(error.message || '获取版本列表失败'));
    }
  }

  /**
   * 激活指定版本
   */
  public async activateVersion(req: MyRequest, res: Response): Promise<void> {
    try {
      const { version } = req.params;
      
      if (!version) {
        res.status(400).json(errorResponse('版本号不能为空'));
        return;
      }
      
      const success = await farmConfigService.activateVersion(version);
      
      if (!success) {
        res.status(400).json(errorResponse('激活版本失败，请检查版本号是否存在'));
        return;
      }
      
      res.json(successResponse({
        version,
        message: '版本激活成功'
      }));
    } catch (error: any) {
      console.error('激活版本失败:', error);
      res.status(500).json(errorResponse(error.message || '激活版本失败'));
    }
  }

  /**
   * 删除指定版本
   */
  public async deleteVersion(req: MyRequest, res: Response): Promise<void> {
    try {
      const { version } = req.params;
      
      if (!version) {
        res.status(400).json(errorResponse('版本号不能为空'));
        return;
      }
      
      const success = await farmConfigService.deleteVersion(version);
      
      if (!success) {
        res.status(400).json(errorResponse('删除版本失败，可能版本不存在或为当前激活版本'));
        return;
      }
      
      res.json(successResponse({
        version,
        message: '版本删除成功'
      }));
    } catch (error: any) {
      console.error('删除版本失败:', error);
      res.status(500).json(errorResponse(error.message || '删除版本失败'));
    }
  }

  /**
   * 回滚到上一版本
   */
  public async rollbackToPrevious(req: MyRequest, res: Response): Promise<void> {
    try {
      const previousVersion = await farmConfigService.rollbackToPrevious();
      
      if (!previousVersion) {
        res.status(400).json(errorResponse('回滚失败，没有可用的历史版本'));
        return;
      }
      
      res.json(successResponse({
        previousVersion,
        message: `已成功回滚到版本: ${previousVersion}`
      }));
    } catch (error: any) {
      console.error('回滚失败:', error);
      res.status(500).json(errorResponse(error.message || '回滚失败'));
    }
  }

  /**
   * 获取配置模板文件
   */
  public async getConfigTemplate(req: MyRequest, res: Response): Promise<void> {
    try {
      const XLSX = require('xlsx');
      
      // 创建模板数据
      const templateData = [
        ['grade', 'production', 'cow', 'speed', 'milk', 'cost', 'offline'],
        ['等级', '每秒产出计算用值', '奶牛数量', '生产速度百分比', '牛奶生产', '升级花费', '离线产出'],
        ['int', 'int', 'int', 'int', 'int', 'int', '1nt'],
        [0, 0, 0, 0, 0, 13096, 0],
        [1, 182, 1, 100, 60.6286626604159, 20043, 90.9429939906239],
        [2, 232, 1, 100, 77.3272817972705, 28583, 115.990922695906],
        // 添加更多示例数据...
      ];

      // 创建工作簿
      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.aoa_to_sheet(templateData);
      
      // 设置列宽
      worksheet['!cols'] = [
        { width: 8 },  // grade
        { width: 12 }, // production  
        { width: 8 },  // cow
        { width: 10 }, // speed
        { width: 15 }, // milk
        { width: 12 }, // cost
        { width: 15 }  // offline
      ];

      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');

      // 生成Excel缓冲区
      const excelBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

      // 设置响应头
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', 'attachment; filename="farm_config_template.xlsx"');
      
      // 发送文件
      res.send(excelBuffer);
    } catch (error: any) {
      console.error('生成模板失败:', error);
      res.status(500).json(errorResponse(error.message || '生成模板失败'));
    }
  }

  /**
   * 获取缓存统计信息
   */
  public async getCacheStats(req: MyRequest, res: Response): Promise<void> {
    try {
      const stats = farmConfigService.getCacheStats();
      
      res.json(successResponse({
        cacheSize: stats.size,
        cacheKeys: stats.keys,
        message: '获取缓存统计成功'
      }));
    } catch (error: any) {
      console.error('获取缓存统计失败:', error);
      res.status(500).json(errorResponse(error.message || '获取缓存统计失败'));
    }
  }

  /**
   * 预览配置文件内容
   */
  public async previewConfig(req: MyRequest, res: Response): Promise<void> {
    try {
      if (!req.file) {
        res.status(400).json(errorResponse('请上传Excel文件'));
        return;
      }

      const XLSX = require('xlsx');
      
      // 读取Excel文件
      const workbook = XLSX.read(req.file.buffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];
      
      if (!sheetName) {
        res.status(400).json(errorResponse('Excel文件中没有找到工作表'));
        return;
      }
      
      const worksheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
      
      // 解析前几行数据用于预览
      const previewData = jsonData.slice(0, 100).map((row: any, index: number) => ({
        rowIndex: index + 1,
        data: Array.isArray(row) ? row : [row]
      }));
      
      res.json(successResponse({
        fileName: req.file.originalname,
        fileSize: req.file.size,
        sheetName: sheetName,
        totalRows: jsonData.length,
        previewData: previewData,
        message: '文件预览成功'
      }));
    } catch (error: any) {
      console.error('预览配置文件失败:', error);
      res.status(500).json(errorResponse(error.message || '预览失败'));
    }
  }

  /**
   * 初始化默认配置
   */
  public async initializeDefaultConfig(req: MyRequest, res: Response): Promise<void> {
    try {
      await farmConfigService.initializeDefaultConfig();
      
      res.json(successResponse({
        message: '默认配置初始化成功'
      }));
    } catch (error: any) {
      console.error('初始化默认配置失败:', error);
      res.status(500).json(errorResponse(error.message || '初始化失败'));
    }
  }
}

export default new FarmConfigController();
