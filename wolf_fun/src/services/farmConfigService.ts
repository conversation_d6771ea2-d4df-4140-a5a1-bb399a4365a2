import FarmConfig from '../models/FarmConfig';
import { sequelize } from '../config/db';
import * as XLSX from 'xlsx';
import { Op, QueryTypes } from 'sequelize';

// 缓存相关
interface CacheData {
  data: FarmConfig[];
  expireAt: number;
}

class FarmConfigService {
  private cache: Map<string, CacheData> = new Map();
  private readonly CACHE_TTL = 3600 * 1000; // 1小时缓存

  /**
   * 获取当前激活的配置（带缓存）
   */
  async getCurrentConfig(): Promise<FarmConfig[]> {
    const cacheKey = 'active_config';
    const cached = this.cache.get(cacheKey);
    
    // 检查缓存是否有效
    if (cached && cached.expireAt > Date.now()) {
      return cached.data;
    }

    // 从数据库获取
    const configs = await FarmConfig.getActiveConfigs();
    
    // 更新缓存
    this.cache.set(cacheKey, {
      data: configs,
      expireAt: Date.now() + this.CACHE_TTL
    });

    return configs;
  }

  /**
   * 根据等级获取配置
   */
  async getConfigByGrade(grade: number): Promise<FarmConfig | null> {
    const cacheKey = `grade_${grade}`;
    const cached = this.cache.get(cacheKey);
    
    if (cached && cached.expireAt > Date.now() && cached.data.length > 0) {
      return cached.data[0];
    }

    const config = await FarmConfig.getConfigByGrade(grade);
    
    if (config) {
      this.cache.set(cacheKey, {
        data: [config],
        expireAt: Date.now() + this.CACHE_TTL
      });
    }

    return config;
  }

  /**
   * 上传新配置
   */
  async uploadNewConfig(
    excelBuffer: Buffer, 
    versionName?: string, 
    description?: string,
    createdBy?: string
  ): Promise<string> {
    try {
      // 1. 解析Excel文件
      const configData = await this.parseExcelFile(excelBuffer);
      
      // 2. 验证数据
      this.validateConfigData(configData);
      
      // 3. 生成版本号
      const version = this.generateVersion(versionName);
      
      // 4. 保存到数据库
      await this.saveConfigToDatabase(configData, version, description, createdBy);
      
      // 5. 清除缓存
      this.clearCache();
      
      return version;
    } catch (error) {
      console.error('上传配置失败:', error);
      throw error;
    }
  }

  /**
   * 激活指定版本
   */
  async activateVersion(version: string): Promise<boolean> {
    try {
      const result = await FarmConfig.activateVersion(version);
      
      if (result) {
        // 清除缓存
        this.clearCache();
      }
      
      return result;
    } catch (error) {
      console.error('激活版本失败:', error);
      return false;
    }
  }

  /**
   * 获取所有版本
   */
  async getAllVersions(): Promise<{ 
    version: string; 
    name?: string; 
    isActive: boolean; 
    configCount: number; 
    createdAt: Date;
    createdBy?: string;
    remark?: string;
  }[]> {
    try {
      const versions = await sequelize.query(`
        SELECT 
          version,
          MIN(remark) as name,
          MAX(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as isActive,
          COUNT(*) as configCount,
          MIN(created_at) as createdAt,
          MIN(created_by) as createdBy,
          MIN(remark) as remark
        FROM farm_configs 
        GROUP BY version 
        ORDER BY created_at DESC
      `, {
        type: QueryTypes.SELECT
      }) as any[];

      return versions.map(v => ({
        version: v.version,
        name: v.name,
        isActive: Boolean(v.isActive),
        configCount: parseInt(v.configCount),
        createdAt: new Date(v.createdAt),
        createdBy: v.createdBy,
        remark: v.remark
      }));
    } catch (error) {
      console.error('获取版本列表失败:', error);
      return [];
    }
  }

  /**
   * 删除版本
   */
  async deleteVersion(version: string): Promise<boolean> {
    try {
      const result = await FarmConfig.deleteVersion(version);
      
      if (result) {
        this.clearCache();
      }
      
      return result;
    } catch (error) {
      console.error('删除版本失败:', error);
      return false;
    }
  }

  /**
   * 回滚到上一版本
   */
  async rollbackToPrevious(): Promise<string | null> {
    try {
      const versions = await this.getAllVersions();
      
      if (versions.length < 2) {
        throw new Error('没有可回滚的版本');
      }
      
      // 找到当前激活版本的索引
      const activeIndex = versions.findIndex(v => v.isActive);
      if (activeIndex === -1) {
        throw new Error('未找到当前激活版本');
      }
      
      // 获取上一个版本
      const previousVersion = versions[activeIndex + 1];
      if (!previousVersion) {
        throw new Error('没有更早的版本可回滚');
      }
      
      // 激活上一版本
      const success = await this.activateVersion(previousVersion.version);
      
      return success ? previousVersion.version : null;
    } catch (error) {
      console.error('回滚失败:', error);
      return null;
    }
  }

  /**
   * 解析Excel文件
   */
  private async parseExcelFile(excelBuffer: Buffer): Promise<any[]> {
    try {
      const workbook = XLSX.read(excelBuffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];
      
      if (!sheetName) {
        throw new Error('Excel文件中没有找到工作表');
      }
      
      const worksheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
      
      if (jsonData.length < 2) {
        throw new Error('Excel文件数据不足');
      }
      
      // 获取标题行和数据行
      const headers = jsonData[0] as string[];
      const dataRows = jsonData.slice(3); // 跳过标题行、中文说明行、数据类型行
      
      // 解析数据
      const configData = (dataRows as any[]).map((row: any, index: number) => {
        if (!Array.isArray(row) || row.length < 7) {
          return null;
        }
        
        return {
          grade: this.parseNumber(row[0], `第${index + 4}行grade字段`),
          production: this.parseNumber(row[1], `第${index + 4}行production字段`),
          cow: this.parseNumber(row[2], `第${index + 4}行cow字段`),
          speed: this.parseNumber(row[3], `第${index + 4}行speed字段`),
          milk: this.parseFloat(row[4], `第${index + 4}行milk字段`),
          cost: this.parseNumber(row[5], `第${index + 4}行cost字段`),
          offline: this.parseFloat(row[6], `第${index + 4}行offline字段`)
        };
      }).filter(item => item !== null);
      
      return configData;
    } catch (error) {
      console.error('解析Excel文件失败:', error);
      throw new Error(`Excel文件解析失败: ${(error as Error).message}`);
    }
  }

  /**
   * 解析数字
   */
  private parseNumber(value: any, fieldName: string): number {
    if (value === null || value === undefined || value === '') {
      return 0;
    }
    
    const num = Number(value);
    if (isNaN(num)) {
      throw new Error(`${fieldName}不是有效数字: ${value}`);
    }
    
    return Math.floor(num);
  }

  /**
   * 解析浮点数
   */
  private parseFloat(value: any, fieldName: string): number {
    if (value === null || value === undefined || value === '') {
      return 0;
    }
    
    const num = Number(value);
    if (isNaN(num)) {
      throw new Error(`${fieldName}不是有效数字: ${value}`);
    }
    
    return num;
  }

  /**
   * 验证配置数据
   */
  private validateConfigData(configData: any[]): void {
    if (!Array.isArray(configData) || configData.length === 0) {
      throw new Error('配置数据为空');
    }
    
    if (configData.length !== 51) {
      throw new Error(`配置数据应包含51条记录（等级0-50），实际: ${configData.length}条`);
    }
    
    // 验证等级连续性
    const grades = configData.map(item => item.grade).sort((a, b) => a - b);
    for (let i = 0; i <= 50; i++) {
      if (grades[i] !== i) {
        throw new Error(`缺少等级${i}的配置数据`);
      }
    }
    
    // 验证数值有效性
    configData.forEach((item, index) => {
      if (item.grade < 0 || item.grade > 50) {
        throw new Error(`第${index + 1}条记录的等级无效: ${item.grade}`);
      }
      
      if (item.production < 0) {
        throw new Error(`第${index + 1}条记录的production不能为负数: ${item.production}`);
      }
      
      if (item.cow < 0) {
        throw new Error(`第${index + 1}条记录的cow不能为负数: ${item.cow}`);
      }
      
      if (item.speed < 0) {
        throw new Error(`第${index + 1}条记录的speed不能为负数: ${item.speed}`);
      }
      
      if (item.milk < 0) {
        throw new Error(`第${index + 1}条记录的milk不能为负数: ${item.milk}`);
      }
      
      if (item.cost < 0) {
        throw new Error(`第${index + 1}条记录的cost不能为负数: ${item.cost}`);
      }
      
      if (item.offline < 0) {
        throw new Error(`第${index + 1}条记录的offline不能为负数: ${item.offline}`);
      }
    });
  }

  /**
   * 生成版本号
   */
  private generateVersion(versionName?: string): string {
    const timestamp = new Date().toISOString().replace(/[-:T.]/g, '').substring(0, 14);
    return versionName ? `${versionName}-${timestamp}` : timestamp;
  }

  /**
   * 保存配置到数据库
   */
  private async saveConfigToDatabase(
    configData: any[], 
    version: string, 
    description?: string,
    createdBy?: string
  ): Promise<void> {
    const transaction = await sequelize.transaction();
    
    try {
      // 批量插入配置数据
      const configRecords = configData.map(item => ({
        ...item,
        version,
        isActive: false,
        createdBy,
        remark: description
      }));
      
      await FarmConfig.bulkCreate(configRecords, { transaction });
      
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * 清除缓存
   */
  private clearCache(): void {
    this.cache.clear();
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }

  /**
   * 初始化默认配置（从fram.xlsx数据）
   */
  async initializeDefaultConfig(): Promise<void> {
    try {
      // 检查是否已有配置
      const existingConfigs = await FarmConfig.findAll({ limit: 1 });
      if (existingConfigs.length > 0) {
        console.log('配置已存在，跳过初始化');
        return;
      }

      // fram.xlsx中的原始数据
      const defaultConfigData = [
        { grade: 0, production: 0, cow: 0, speed: 0, milk: 0, cost: 13096, offline: 0 },
        { grade: 1, production: 182, cow: 1, speed: 100, milk: 60.6286626604159, cost: 20043, offline: 90.9429939906239 },
        { grade: 2, production: 232, cow: 1, speed: 100, milk: 77.3272817972705, cost: 28583, offline: 115.990922695906 },
        { grade: 3, production: 276, cow: 2, speed: 110, milk: 95.0646914480305, cost: 39214, offline: 137.843802599644 },
        { grade: 4, production: 315, cow: 2, speed: 110, milk: 108.683909147742, cost: 52496, offline: 157.591668264226 },
        { grade: 5, production: 352, cow: 3, speed: 120, milk: 125.578116496437, cost: 69100, offline: 175.809363095011 },
        { grade: 6, production: 386, cow: 3, speed: 120, milk: 137.74696498783, cost: 89837, offline: 192.845750982962 },
        { grade: 7, production: 418, cow: 4, speed: 130, milk: 154.764544585978, cost: 115699, offline: 208.93213519107 },
        { grade: 8, production: 448, cow: 4, speed: 130, milk: 166.097458615402, cost: 147898, offline: 224.231569130793 },
        { grade: 9, production: 478, cow: 5, speed: 140, milk: 183.741771024691, cost: 187923, offline: 238.864302332098 },
        { grade: 10, production: 506, cow: 5, speed: 140, milk: 194.555498450395, cost: 237594, offline: 252.922147985514 },
        { grade: 11, production: 533, cow: 6, speed: 150, milk: 213.1817313526, cost: 299139, offline: 266.47716419075 },
        { grade: 12, production: 559, cow: 6, speed: 150, milk: 223.669748161731, cost: 375289, offline: 279.587185202164 },
        { grade: 13, production: 585, cow: 7, speed: 160, milk: 243.582916288346, cost: 469380, offline: 292.299499546015 },
        { grade: 14, production: 609, cow: 7, speed: 160, milk: 253.87781959937, cost: 585495, offline: 304.653383519244 },
        { grade: 15, production: 633, cow: 8, speed: 170, milk: 275.375563987387, cost: 728621, offline: 316.681898585495 },
        { grade: 16, production: 657, cow: 8, speed: 170, milk: 285.576694795747, cost: 904851, offline: 328.413199015109 },
        { grade: 17, production: 680, cow: 9, speed: 180, milk: 308.974094605604, cost: 1121623, offline: 339.871504066165 },
        { grade: 18, production: 702, cow: 9, speed: 180, milk: 319.161667767775, cost: 1388015, offline: 351.077834544553 },
        { grade: 19, production: 724, cow: 10, speed: 190, milk: 344.810076374009, cost: 1715098, offline: 362.05058019271 },
        { grade: 20, production: 746, cow: 10, speed: 190, milk: 355.053279277757, cost: 2116373, offline: 372.805943241645 },
        { grade: 21, production: 767, cow: 11, speed: 200, milk: 383.358289739038, cost: 3568859, offline: 383.358289739038 },
        { grade: 22, production: 1077, cow: 11, speed: 200, milk: 538.717068272049, cost: 4412137, offline: 538.717068272049 },
        { grade: 23, production: 1110, cow: 12, speed: 200, milk: 555.007842049574, cost: 5448042, offline: 555.007842049574 },
        { grade: 24, production: 1142, cow: 12, speed: 200, milk: 571.096181614764, cost: 6719624, offline: 571.096181614764 },
        { grade: 25, production: 1174, cow: 13, speed: 200, milk: 586.992556305504, cost: 8279413, offline: 586.992556305504 },
        { grade: 26, production: 1205, cow: 13, speed: 200, milk: 602.706513978308, cost: 10191468, offline: 602.706513978308 },
        { grade: 27, production: 1236, cow: 14, speed: 200, milk: 618.246793110461, cost: 12533893, offline: 618.246793110461 },
        { grade: 28, production: 1267, cow: 14, speed: 200, milk: 633.621417760574, cost: 15401872, offline: 633.621417760574 },
        { grade: 29, production: 1298, cow: 15, speed: 210, milk: 682.987135271378, cost: 18911373, offline: 648.837778507809 },
        { grade: 30, production: 1328, cow: 15, speed: 210, milk: 698.844949302347, cost: 23203639, offline: 663.902701837229 },
        { grade: 31, production: 1358, cow: 16, speed: 220, milk: 754.247233265651, cost: 28450646, offline: 678.822509939086 },
        { grade: 32, production: 1387, cow: 16, speed: 220, milk: 770.670080559791, cost: 34861724, offline: 693.603072503812 },
        { grade: 33, production: 1416, cow: 17, speed: 230, milk: 833.23511975717, cost: 42691606, offline: 708.249851793595 },
        { grade: 34, production: 1446, cow: 17, speed: 230, milk: 850.315225923342, cost: 52250188, offline: 722.767942034841 },
        { grade: 35, production: 1474, cow: 18, speed: 240, milk: 921.452629985649, cost: 63914377, offline: 737.162103988519 },
        { grade: 36, production: 1503, cow: 18, speed: 240, milk: 939.295994257361, cost: 78142467, offline: 751.436795405888 },
        { grade: 37, production: 1531, cow: 19, speed: 250, milk: 1020.79493060936, cost: 95491578, offline: 765.596197957023 },
        { grade: 38, production: 1559, cow: 19, speed: 250, milk: 1039.52565482997, cost: 116638812, offline: 779.644241122478 },
        { grade: 39, production: 1587, cow: 20, speed: 260, milk: 1133.69231922783, cost: 142406902, offline: 793.584623459482 },
        { grade: 40, production: 1615, cow: 20, speed: 260, milk: 1153.45833084203, cost: 173795324, offline: 807.420831589424 },
        { grade: 41, production: 1642, cow: 20, speed: 270, milk: 1263.31716492355, cost: 308830081, offline: 821.156157200305 },
        { grade: 42, production: 2432, cow: 20, speed: 270, milk: 1870.73666517117, cost: 377475021, offline: 1215.97883236126 },
        { grade: 43, production: 2477, cow: 20, speed: 280, milk: 2064.24927715172, cost: 461187294, offline: 1238.54956629103 },
        { grade: 44, production: 2522, cow: 20, speed: 280, milk: 2101.6965485301, cost: 563241743, offline: 1261.01792911806 },
        { grade: 45, production: 2567, cow: 20, speed: 290, milk: 2333.43026387914, cost: 687619368, offline: 1283.38664513353 },
        { grade: 46, production: 2611, cow: 20, speed: 290, milk: 2373.92419743442, cost: 839158602, offline: 1305.65830858893 },
        { grade: 47, production: 2656, cow: 20, speed: 290, milk: 2414.24616824307, cost: 1023738817, offline: 1327.83539253369 },
        { grade: 48, production: 2700, cow: 20, speed: 290, milk: 2454.40046705588, cost: 1248502902, offline: 1349.92025688074 },
        { grade: 49, production: 2744, cow: 20, speed: 300, milk: 2743.83031156396, cost: 1522127175, offline: 1371.91515578198 },
        { grade: 50, production: 2788, cow: 20, speed: 300, milk: 2787.64448877048, cost: 0, offline: 1393.82224438524 }
      ];

      // 保存默认配置
      await this.saveConfigToDatabase(
        defaultConfigData,
        'default',
        '初始默认配置',
        'system'
      );

      // 激活默认配置
      await FarmConfig.activateVersion('default');

      console.log('默认配置初始化完成');
    } catch (error) {
      console.error('初始化默认配置失败:', error);
      throw error;
    }
  }
}

export default new FarmConfigService();
