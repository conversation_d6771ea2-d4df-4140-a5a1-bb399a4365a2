'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 创建农场配置表
    await queryInterface.createTable('farm_configs', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        comment: '主键'
      },
      grade: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: '等级 (0-50)'
      },
      production: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: '每秒产出计算用值'
      },
      cow: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: '奶牛数量'
      },
      speed: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: '生产速度百分比'
      },
      milk: {
        type: Sequelize.DECIMAL(15, 3),
        allowNull: false,
        comment: '牛奶生产'
      },
      cost: {
        type: Sequelize.BIGINT,
        allowNull: false,
        comment: '升级花费'
      },
      offline: {
        type: Sequelize.DECIMAL(15, 3),
        allowNull: false,
        comment: '离线产出'
      },
      version: {
        type: Sequelize.STRING(50),
        allowNull: false,
        defaultValue: 'default',
        comment: '配置版本'
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: '是否激活'
      },
      created_by: {
        type: Sequelize.STRING(100),
        allowNull: true,
        comment: '创建者'
      },
      remark: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '版本说明'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    }, {
      engine: 'InnoDB',
      charset: 'utf8mb4',
      comment: '农场升级配置表'
    });

    // 创建索引
    await queryInterface.addIndex('farm_configs', ['grade'], {
      name: 'idx_grade'
    });

    await queryInterface.addIndex('farm_configs', ['version'], {
      name: 'idx_version'
    });

    await queryInterface.addIndex('farm_configs', ['is_active'], {
      name: 'idx_active'
    });

    await queryInterface.addIndex('farm_configs', ['created_at'], {
      name: 'idx_created_at'
    });

    // 创建唯一索引
    await queryInterface.addConstraint('farm_configs', {
      fields: ['grade', 'version'],
      type: 'unique',
      name: 'unique_grade_version'
    });
  },

  async down(queryInterface, Sequelize) {
    // 删除表
    await queryInterface.dropTable('farm_configs');
  }
};
